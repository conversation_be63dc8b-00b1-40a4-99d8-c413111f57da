import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../config/app_strings.dart';
import '../../../enum/user_role.dart';
import '../../../theme/app_fonts.dart';
import '/models/user.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import 'mobile_drawer.dart';

class Header extends HookWidget {
  final String selectedTab;
  Header({super.key, required this.selectedTab});

  final User user = User(
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.broker,
  );

  //valuenortifier for tab seelection
  final ValueNotifier<String> _selectedTab = ValueNotifier<String>('');

  // Getter for the mobile drawer
  Widget get mobileDrawer =>
      MobileDrawer(user: user, selectedTab: _selectedTab);

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      _selectedTab.value = selectedTab;
      return null;
    }, []);

    return ValueListenableBuilder(
      valueListenable: _selectedTab,
      builder: (context, value, child) {
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isSmallMobile(context)
                ? defaultPadding * 0.3
                : defaultPadding / 2,
            vertical: Responsive.isSmallMobile(context)
                ? defaultPadding * 0.3
                : defaultPadding / 2,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(12)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                spreadRadius: 0,
                blurRadius: 2,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Mobile menu button
              if (Responsive.showDrawer(context))
                IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                ),
              SizedBox(width: Responsive.isSmallMobile(context) ? 4 : 8),

              // Logo with responsive sizing
              Image.asset(
                '$launcherAssetpath/logo.png',
                height: Responsive.isSmallMobile(context)
                    ? 24
                    : Responsive.isMobile(context)
                        ? 28
                        : 32,
              ),

              // Desktop navigation items
              if (!Responsive.showDrawer(context)) ...[
                SizedBox(width: Responsive.isTablet(context) ? 12 : 20),
                _buildNavItem(
                  context,
                  dashboardTab,
                  isSelected: _selectedTab.value == dashboardTab,
                ),
                _buildNavItem(
                  context,
                  brokersTab,
                  isSelected: _selectedTab.value == brokersTab,
                ),
                _buildNavItem(
                  context,
                  agentsTab,
                  isSelected: _selectedTab.value == agentsTab,
                ),
                _buildNavItem(
                  context,
                  salesTab,
                  isSelected: _selectedTab.value == salesTab,
                ),
                _buildNavItem(
                  context,
                  commissionTab,
                  isSelected: _selectedTab.value == commissionTab,
                ),
                if (user.role != UserRole.agent)
                  _buildNavItem(
                    context,
                    reportsTab,
                    isSelected: _selectedTab.value == reportsTab,
                  ),

                const Spacer(),
                // if (user.role != UserRole.platformOwner)
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: Responsive.isTablet(context)
                          ? defaultPadding * 0.7
                          : defaultPadding,
                      vertical: defaultPadding / 2,
                    ),
                  ),
                  onPressed: () {
                    _selectedTab.value = addNewButton;
                    Navigator.pushNamed(context, '/register-broker');
                  },
                  icon: Icon(
                    Icons.add,
                    size: Responsive.isTablet(context) ? 18 : 20,
                  ),
                  label: Text(
                    addNewButton,
                    style: TextStyle(
                      fontSize: Responsive.isTablet(context) ? 12 : 14,
                    ),
                  ),
                ),
                SizedBox(width: Responsive.isTablet(context) ? defaultPadding * 0.7 : defaultPadding),

                _headerIcon(Icons.notifications_outlined),
                SizedBox(width: Responsive.isTablet(context) ? defaultPadding * 0.7 : defaultPadding),
                _headerIcon(Icons.settings_outlined),
                SizedBox(width: Responsive.isTablet(context) ? defaultPadding * 0.7 : defaultPadding),
              ],
              Responsive.showDrawer(context)
                  ? const Spacer()
                  : const SizedBox.shrink(),

              // Desktop action buttons and profile
              if (!Responsive.isMobile(context)) ...[
                _buildProfileInfo(context),
              ],

              // Mobile profile (simplified)
              if (Responsive.isMobile(context)) ...[
                const Spacer(),
                SizedBox(width: Responsive.isSmallMobile(context) ? 8 : defaultPadding),
                CircleAvatar(
                  backgroundImage: AssetImage(user.image),
                  radius: Responsive.isSmallMobile(context) ? 14 : 16,
                ),
                SizedBox(width: Responsive.isSmallMobile(context) ? 4 : 8),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _headerIcon(IconData icon) {
    return Builder(
      builder: (context) => Container(
        //background color-light grey, circle shape
        decoration: BoxDecoration(
          color: AppTheme.headerIconBgColor,
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.all(Responsive.isTablet(context) ? 4.0 : 5.0),
          child: Icon(
            icon,
            size: Responsive.isTablet(context) ? 18 : 20,
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: () {
          _selectedTab.value = title;
          if (title == dashboardTab) {
            Navigator.pushNamed(context, '/dashboard');

            //do nothing
          } else if (title == brokersTab) {
            //navigate to brokers
          } else if (title == agentsTab) {
            Navigator.pushNamed(context, '/agents');
          } else if (title == salesTab) {
            //navigate to sales
          } else if (title == commissionTab) {
            //navigate to commission
          } else if (title == reportsTab) {
            //navigate to reports
          }
        },
        child: Container(
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(
            // color: Colors.red,
            //     width: 2,
            //   ),
            // ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor)
                    : AppFonts.mediumTextStyle(14, color: Colors.black),
              ),
              // if (isSelected)
              //   Container(
              //     // height: 2,
              //     // width: 90,
              //     child: const TriangleIndicator(),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(
          backgroundImage: AssetImage(user.image),
          radius: Responsive.isTablet(context) ? 16 : 20,
        ),
        SizedBox(width: Responsive.isTablet(context) ? defaultPadding * 0.3 : defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                userRoleToString(user.role),
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        if (Responsive.isDesktop(context))
          Icon(
            Icons.keyboard_arrow_down,
            size: 18,
          ),
      ],
    );
  }
}
