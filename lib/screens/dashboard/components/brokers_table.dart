import 'dart:math' as math;

import 'package:flutter/material.dart';
import '../../../config/json_consts.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../agent_network/agent_network_screen.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '../../../theme/app_fonts.dart';
import '/screens/dashboard/components/rounderd_icon_btn.dart';

import '../../../config/app_strings.dart';

class BrokersTable extends StatefulWidget {
  const BrokersTable({super.key});

  @override
  State<BrokersTable> createState() => _BrokersTableState();
}

class _BrokersTableState extends State<BrokersTable> {
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _currentPage = 1; // Reset to first page when searching
    });
  }

  // Filter brokers based on search query
  List<Broker> get filteredBrokers {
    if (_searchQuery.isEmpty) {
      return brokers;
    }
    return brokers
        .where(
          (broker) =>
              broker.name.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          padding: EdgeInsets.all(defaultPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: defaultPadding),
              _buildTable(context, constraints),
              const SizedBox(height: defaultPadding),
              _buildFooter(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(
                icon: 'user',
                backgroundColor: Colors.transparent,
                onPressed: () {},
                iconSize: 20,
              ),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  brokersTitle,
                  style: AppFonts.semiBoldTextStyle(18),
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: searchHint,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _currentPage = 1; // Reset to first page when searching
                });
              },
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Image.asset('$iconAssetpath/user.png', height: 20, width: 20),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.only(top: 2),
          child: Text(brokersTitle, style: AppFonts.semiBoldTextStyle(22)),
        ),
        const Spacer(),
        SizedBox(
          width: ResponsiveSizes.searchFieldWidth(context),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: searchHint,
              hintStyle: AppFonts.regularTextStyle(
                14,
                color: AppTheme.primaryTextColor,
              ),
              prefixIcon: Container(
                height: 24,
                width: 24,
                padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                child: Image.asset('$iconAssetpath/search.png'),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppTheme.searchbarBg,
              contentPadding: const EdgeInsets.symmetric(vertical: 0),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _currentPage = 1; // Reset to first page when searching
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTable(BuildContext context, BoxConstraints constraints) {
    // Get filtered brokers based on search
    final List<Broker> brokersToDisplay = filteredBrokers;

    // Calculate start and end indices for pagination
    final int startIndex = (_currentPage - 1) * _itemsPerPage;
    final int endIndex = math.min(
      startIndex + _itemsPerPage,
      brokersToDisplay.length,
    );

    // Get the paginated brokers list
    final List<Broker> paginatedBrokers = brokersToDisplay.isEmpty
        ? []
        : brokersToDisplay.sublist(startIndex, endIndex);

    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context, paginatedBrokers);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(context, constraints, paginatedBrokers);
    } else {
      return _buildDesktopTable(context, constraints, paginatedBrokers);
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Broker> paginatedBrokers,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: constraints.maxWidth,
        child: DataTable(
          columnSpacing: defaultPadding * 0.8,
          dataRowMinHeight: 40,
          dataRowMaxHeight: 50,
          columns: [
            _dataColumn(name: brokerColumnHeader),
            _dataColumn(name: contactsColumnHeader),
            _dataColumn(name: emailAddressColumnHeader),
            _dataColumn(name: totalAgentsColumnHeader),
            _dataColumn(name: totalSalesColumnHeader),
            _dataColumn(name: actionsColumnHeader, allowSort: false),
          ],
          rows: List.generate(
            paginatedBrokers.length,
            (index) => _brokerDesktopDataRow(context, paginatedBrokers[index]),
          ),
        ),
      ),
    );
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (name != '')
              Expanded(
                child: Text(
                  name,
                  maxLines: 2,
                  style: AppFonts.mediumTextStyle(
                    fontSize,
                    color: AppTheme.tableColumnHeaderColor,
                  ),
                ),
              ),
            if (allowSort)
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Broker> paginatedBrokers,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        width: constraints.maxWidth,
        child: DataTable(
          columnSpacing: defaultPadding * 0.4,
          dataRowMinHeight: 48,
          dataRowMaxHeight: 52,
          columns: [
            _dataColumn(name: brokerColumnHeader, fontSize: 12),
            _dataColumn(name: contactsColumnHeader, fontSize: 12),
            _dataColumn(name: emailAddressColumnHeader, fontSize: 12),
            _dataColumn(name: totalAgentsColumnHeader, fontSize: 12),
            _dataColumn(name: totalSalesColumnHeader, fontSize: 12),
            _dataColumn(
              name: '      $actionsColumnHeader      ',
              fontSize: 12,
              allowSort: false,
            ),
          ],
          rows: List.generate(
            paginatedBrokers.length,
            (index) => _brokerDataRowTablet(
              context,
              paginatedBrokers[index],
              constraints,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileTable(
    BuildContext context,
    List<Broker> paginatedBrokers,
  ) {
    return Column(
      children: List.generate(
        paginatedBrokers.length,
        (index) => _buildMobileCard(context, paginatedBrokers[index]),
      ),
    );
  }

  Widget _buildMobileCard(BuildContext context, Broker broker) {
    return Container(
      margin: const EdgeInsets.only(bottom: defaultPadding),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isMobile: true,
          ),
          const SizedBox(height: 12),
          _buildMobileCardRow(contactsColumnHeader, broker.contact),
          _buildMobileCardRow(emailAddressColumnHeader, broker.email),
          _buildMobileCardRow(
            totalAgentsColumnHeader,
            broker.agents.length.toString(),
          ),
          _buildMobileCardRow(totalSalesColumnHeader, broker.sales.toString()),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: _ActionButton(
              onPressed: () async => await _showAgentNetwork(broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  DataRow _brokerDesktopDataRow(BuildContext context, Broker broker) {
    return DataRow(
      cells: [
        DataCell(
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isCompact: false,
            isMobile: false,
          ),
        ),
        DataCell(Text(broker.contact, overflow: TextOverflow.ellipsis)),
        DataCell(Text(broker.email, overflow: TextOverflow.ellipsis)),
        DataCell(
          Text(
            broker.agents.length.toString(),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(broker.sales.toString(), overflow: TextOverflow.ellipsis),
        ),
        DataCell(
          _ActionButton(
            onPressed: () async => await _showAgentNetwork(broker),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  DataRow _brokerDataRowTablet(
    BuildContext context,
    Broker broker,
    BoxConstraints constraints,
  ) {
    return DataRow(
      cells: [
        DataCell(
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isCompact: true,
            isMobile: false,
          ),
        ),
        _dataCell(broker.contact, TextOverflow.ellipsis, fontSize: 12),
        _dataCell(broker.email, TextOverflow.ellipsis, fontSize: 12),
        _dataCell(
          broker.agents.length.toString(),
          TextOverflow.ellipsis,
          fontSize: 12,
        ),
        _dataCell(broker.sales.toString(), TextOverflow.ellipsis, fontSize: 12),
        DataCell(
          _ActionButton(
            onPressed: () async => await _showAgentNetwork(broker),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  DataCell _dataCell(
    String value,
    TextOverflow overflow, {
    double fontSize = 14,
  }) => DataCell(
    Text(
      value,
      overflow: overflow,
      style: AppFonts.mediumTextStyle(
        fontSize,
        color: AppTheme.primaryTextColor,
      ),
    ),
  );

  Widget _buildFooter(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          showingDatatext(context),
          const SizedBox(height: defaultPadding),
          _buildPagination(context),
        ],
      );
    }

    return Row(
      children: [
        showingDatatext(context),
        const Spacer(),
        _buildPagination(context),
      ],
    );
  }

  Text showingDatatext(BuildContext context) {
    final List<Broker> brokersToDisplay = filteredBrokers;
    final int startIndex = brokersToDisplay.isEmpty
        ? 0
        : (_currentPage - 1) * _itemsPerPage + 1;
    final int endIndex = math.min(
      _currentPage * _itemsPerPage,
      brokersToDisplay.length,
    );

    return Text(
      "$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel ${brokersToDisplay.length} $showingDataLabelP2",
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  Widget _buildPagination(BuildContext context) {
    // Calculate total pages based on broker length
    final int totalPages = (filteredBrokers.length / _itemsPerPage).ceil();

    // Create pagination buttons based on current page and total pages
    List<Widget> paginationButtons = [];

    // Add left arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_left,
        onTap: _currentPage > 1 ? () => setState(() => _currentPage--) : null,
      ),
    );

    // Add page numbers
    if (Responsive.isMobile(context) || totalPages <= 5) {
      // For mobile or few pages, show all pages or just 3
      final int pagesToShow = Responsive.isMobile(context) ? 3 : totalPages;
      for (int i = 1; i <= pagesToShow; i++) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == _currentPage,
            onTap: () => setState(() => _currentPage = i),
          ),
        );
      }
    } else {
      // For desktop with many pages, show current page with neighbors and ellipsis
      // Always show first page
      paginationButtons.add(
        _paginationButton(
          label: "1",
          isSelected: 1 == _currentPage,
          onTap: () => setState(() => _currentPage = 1),
        ),
      );

      // Show ellipsis if current page is not near the beginning
      if (_currentPage > 3) {
        paginationButtons.add(_paginationButton(label: "..."));
      }

      // Show pages around current page
      for (
        int i = math.max(2, _currentPage - 1);
        i <= math.min(totalPages - 1, _currentPage + 1);
        i++
      ) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == _currentPage,
            onTap: () => setState(() => _currentPage = i),
          ),
        );
      }

      // Show ellipsis if current page is not near the end
      if (_currentPage < totalPages - 2) {
        paginationButtons.add(_paginationButton(label: "..."));
      }

      // Always show last page
      if (totalPages > 1) {
        paginationButtons.add(
          _paginationButton(
            label: "$totalPages",
            isSelected: totalPages == _currentPage,
            onTap: () => setState(() => _currentPage = totalPages),
          ),
        );
      }
    }

    // Add right arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_right,
        onTap: _currentPage < totalPages
            ? () => setState(() => _currentPage++)
            : null,
      ),
    );

    return Row(
      mainAxisAlignment: Responsive.isMobile(context)
          ? MainAxisAlignment.center
          : MainAxisAlignment.start,
      children: paginationButtons,
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    final bool isDisabled = onTap == null;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 5),
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: icon != null
              ? Icon(
                  icon,
                  size: 16,
                  color: isDisabled
                      ? Colors.grey.shade400
                      : (isSelected ? Colors.white : Colors.black),
                )
              : Text(
                  label!,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontSize: 12,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildMobileCardRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  _showAgentNetwork(Broker broker) async {
    // TODO: loader, api call
    if (!mounted) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentNetworkScreen(selectedBroker: broker),
      ),
    );
  }
}

// Reusable Components
class _BrokerNameCell extends StatelessWidget {
  final String name;
  final VoidCallback onViewPressed;
  final bool isCompact;
  final bool isMobile;

  const _BrokerNameCell({
    required this.name,
    required this.onViewPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return Container(
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(name, style: AppFonts.boldTextStyle(14))),
          ],
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          '$iconAssetpath/agent_round.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        SizedBox(width: isCompact ? 4 : 8),
        Flexible(
          child: Text(
            name,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.mediumTextStyle(
              isCompact ? 12 : 14,
              color: AppTheme.primaryTextColor,
            ),
          ),
        ),
      ],
    );
  }
}

class _ActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;

  const _ActionButton({
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return TextButton.icon(
        onPressed: onPressed,
        icon: Image.asset(
          '$iconAssetpath/eye.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        label: Text(
          viewAgents,
          style: AppFonts.regularTextStyle(14, color: AppTheme.blueCardColor),
        ),
      );
    }

    final iconSize = isCompact ? 12.0 : 16.0;
    final alignment = isCompact ? Alignment.centerRight : Alignment.center;
    final maxWidth = isCompact ? 60.0 : double.infinity;

    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Align(
        alignment: alignment,
        child: IconButton(
          onPressed: onPressed,
          highlightColor: AppTheme.roundIconBgColor,
          hoverColor: AppTheme.roundIconBgColor,
          alignment: Alignment.centerRight,
          tooltip: viewAgents,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
          icon: Container(
            height: 28,
            width: 28,
            decoration: BoxDecoration(
              color: AppTheme.roundIconBgColor,
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsets.all(6),
            child: Image.asset(
              '$iconAssetpath/eye.png',
              height: iconSize,
              width: iconSize,
            ),
          ),
        ),
      ),
    );
  }
}
